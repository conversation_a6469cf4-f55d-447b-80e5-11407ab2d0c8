{"name": "vite-vue3", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@vueuse/core": "10.11.0", "file-saver": "2.0.5", "js-cookie": "3.0.5", "axios": "^1.3.4", "docx-preview": "^0.1.16", "echarts": "^5.4.2", "element-plus": "^2.3.1", "pinia": "^2.0.33", "qs": "^6.11.2", "sass": "^1.59.3", "vue": "^3.2.47", "vue-clipboard2": "^0.3.3", "vue-router": "^4.1.6", "vue-ueditor-wrap": "^3.0.8", "vuedraggable": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.21.3", "@babel/eslint-parser": "^7.21.3", "@vitejs/plugin-legacy": "^4.0.2", "@vitejs/plugin-vue": "^4.1.0", "qs": "^6.11.2", "unplugin-auto-import": "^0.15.1", "unplugin-vue-components": "^0.24.1", "vite": "^4.2.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1"}}
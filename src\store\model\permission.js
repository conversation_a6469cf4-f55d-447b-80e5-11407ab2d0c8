import constantRoutes from '@/router/mainRouter';
import { defineStore } from 'pinia';
// 匹配views里面所有的.vue文件
const modules = import.meta.glob('./../../views/**/*.vue');
const usePermissionStore = defineStore('permission', {
  state: () => ({
    routes: [],
    addRoutes: [],
    defaultRoutes: [],
    topbarRouters: [],
    sidebarRouters: [],
  }),
  actions: {
    setRoutes(routes) {
      this.addRoutes = routes;
      this.routes = constantRoutes.concat(routes);
    },
    setDefaultRoutes(routes) {
      this.defaultRoutes = constantRoutes.concat(routes);
    },
    setTopbarRoutes(routes) {
      this.topbarRouters = routes;
    },
    setSidebarRouters(routes) {
      this.sidebarRouters = routes;
    },
    generateRoutes(roles) {
      return new Promise((resolve) => {
        this.setRoutes([]);
        this.setSidebarRouters(constantRoutes);
        this.setDefaultRoutes([]);
        this.setTopbarRoutes([]);
        resolve(this.routes);
      });
    },
  },
});

export default usePermissionStore;

<template>
  <div class="login-bg" :style="'background-image:url(' + bgImg + ');'">
    <img :src="logoImg" class="logo-img" />
    <img :src="supplierNameImg" class="login-title" />
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-position="left"
      label-width="0px"
      class="demo-ruleForm login-container"
    >
      <h3 class="title" style="margin-bottom: 20px">账号密码登录</h3>
      <!--用户名-->
      <el-form-item prop="account"
        ><el-input
          type="text"
          v-model="ruleForm.account"
          auto-complete="off"
          placeholder="请输入账号"
        ></el-input
      ></el-form-item>
      <!--密码-->
      <el-form-item prop="checkPass"
        ><el-input
          type="password"
          v-model="ruleForm.checkPass"
          auto-complete="off"
          placeholder="请输入密码"
        ></el-input
      ></el-form-item>
      <!--验证码-->
      <el-form-item prop="captcha">
        <div class="captcha-container">
          <el-input
            type="text"
            v-model="ruleForm.captcha"
            auto-complete="off"
            placeholder="请输入验证码"
            class="captcha-input"
          ></el-input>
          <div class="captcha-code" @click="refreshCaptcha">
            <span
              v-for="(char, index) in captchaText"
              :key="index"
              :style="getCaptchaCharStyle(index)"
              class="captcha-char"
            >
              {{ char }}
            </span>
          </div>
        </div>
      </el-form-item>
      <!--登录-->
      <el-form-item
        ><el-button
          class="login-btn"
          type="primary"
          style="width: 100%"
          @click.native.prevent="SubmitFunc"
          :loading="logining"
          >登录</el-button
        ></el-form-item
      >
    </el-form>
  </div>
</template>

<script>
import IndexApi from '@/api/index.js';
import bgImg from '@/assets/img/login-bg.png';
import supplierNameImg from '@/assets/img/logo-title.png';
import logoImg from '@/assets/img/logo.png';
import UserApi from '@/api/user.js';
import { useUserStore } from '@/store';
const { afterLogin } = useUserStore();

export default {
  data() {
    return {
      logoImg,
      supplierNameImg,
      /*是否正在加载*/
      loading: true,
      /*背景图片*/
      bgImg,
      /*是否正在提交*/
      logining: false,
      /*表单对象*/
      ruleForm: {
        /*用户名*/
        account: '',
        /*密码*/
        checkPass: '',
        /*验证码*/
        captcha: '',
      },
      /*验证规则*/
      rules: {
        /*用户名*/
        account: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        /*密码*/
        checkPass: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        /*验证码*/
        captcha: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
      },
      /*验证码相关*/
      captchaText: '',
      captchaValue: '',
      captchaColors: [
        '#FF6B6B',
        '#4ECDC4',
        '#45B7D1',
        '#96CEB4',
        '#FFEAA7',
        '#DDA0DD',
        '#98D8C8',
        '#F7DC6F',
      ],
      /*基础配置*/
      baseData: {},
    };
  },
  created() {
    this.getData();
    this.generateCaptcha();
    // 添加验证码验证规则
    this.rules.captcha.push({
      validator: this.validateCaptcha,
      trigger: 'blur',
    });
    if (this.$route.query.from && this.$route.query.from == 'admin') {
      this.saasLogin();
    }
  },
  methods: {
    /*获取基础配置*/
    getData() {
      let self = this;
      IndexApi.base(true)
        .then((res) => {
          self.loading = false;
          const {
            data: { supplierBgImg, supplierName },
          } = res;
          if (supplierBgImg) {
            self.bgImg = supplierBgImg;
          }
          if (supplierName) {
            self.supplierNameImg = supplierName;
          }
        })
        .catch((error) => {
          self.loading = false;
        });
    },

    /*生成验证码*/
    generateCaptcha() {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
      let result = '';
      for (let i = 0; i < 4; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      this.captchaText = result;
      this.captchaValue = result;
    },

    /*刷新验证码*/
    refreshCaptcha() {
      this.generateCaptcha();
      this.ruleForm.captcha = '';
    },

    /*获取验证码字符样式*/
    getCaptchaCharStyle(index) {
      const colors = this.captchaColors;
      const color = colors[index % colors.length];
      const rotation = (Math.random() - 0.5) * 30; // -15度到15度的随机旋转
      const fontSize = 18 + Math.random() * 4; // 18-22px的随机字体大小

      return {
        color: color,
        transform: `rotate(${rotation}deg)`,
        fontSize: `${fontSize}px`,
        fontWeight: 'bold',
        display: 'inline-block',
        margin: '0 2px',
        textShadow: '1px 1px 2px rgba(0,0,0,0.1)',
      };
    },

    /*验证码校验*/
    validateCaptcha(rule, value, callback) {
      if (!value) {
        callback(new Error('请输入验证码'));
      } else if (value.toUpperCase() !== this.captchaValue.toUpperCase()) {
        callback(new Error('验证码错误'));
      } else {
        callback();
      }
    },

    /*登录方法*/
    SubmitFunc(ev) {
      var _this = this;
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.logining = true;
          var Params = {
            username: this.ruleForm.account,
            password: this.ruleForm.checkPass,
            captcha: this.ruleForm.captcha,
          };
          /*调用登录接口*/
          UserApi.login(Params, true)
            .then(async (res) => {
              this.logining = false;
              if (res.code == 1) {
                await afterLogin(res);
                this.logining = false;
                this.$router.push({
                  path: '/',
                });
              } else {
                ElMessage({
                  message: '登录失败',
                  type: 'error',
                });
                this.refreshCaptcha();
              }
            })
            .catch((error) => {
              this.logining = false;
              this.refreshCaptcha();
            });
        }
      });
    },
    saasLogin() {
      /*调用登录接口*/
      UserApi.saasLogin({}, true)
        .then((res) => {
          console.log(res);
          if (res.code == 1) {
            delCookie('baseInfo');
            /*保存个人信息*/
            setCookie('userinfo', res.data);
            /*设置一个登录状态*/
            setCookie('isLogin', true);
            /*跳转到首页*/
            this.$router.push({ path: '/' });
          } else {
            ElMessage({
              message: '登录失败',
              type: 'error',
            });
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.login-bg {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.login-container {
  border-radius: 10px;
  -moz-border-radius: 10px;
  background-clip: padding-box;
  padding: 35px 70px;
  background: #fff;
  .title {
    margin: 0px auto 40px auto;
    text-align: center;
    color: #222222;
    font-size: 28px;
    font-weight: 400;
  }
  .remember {
    margin: 0px 0px 35px 0px;
  }
}
.logo-img {
  width: 100%;
  height: 70px;
  object-fit: contain;
}
.login-title {
  width: 100%;
  height: 41px;
  object-fit: contain;
  margin-top: 20px;
  margin-bottom: 50px;
}

:deep(.el-input__wrapper) {
  border-radius: 10px;
  box-shadow: none;
  border: 2px solid transparent;
  width: 400px;
  height: 60px;
  background: #f7f7f7;
  font-size: 18px;
}
:deep(.el-input__wrapper.is-focus) {
  border: 2px solid #ef6901;
}
.login-btn {
  width: 400px;
  height: 60px;
  background: #ef6901;
  border-radius: 10px;
  color: #fff;
  font-weight: 500;
  font-size: 18px;
  border: none;
}

/* 验证码样式 */
.captcha-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.captcha-input {
  flex: 1;
}

.captcha-code {
  width: 120px;
  height: 60px;
  background: #f3f3f3;
  border: 2px solid transparent;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.captcha-code:hover {
  border-color: #ef6901;
  background: #f0f0f0;
}

.captcha-char {
  font-family: 'Arial', sans-serif;
  letter-spacing: 2px;
  transition: all 0.3s ease;
}

.captcha-code::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 2px,
    rgba(0, 0, 0, 0.05) 2px,
    rgba(0, 0, 0, 0.05) 4px
  );
  pointer-events: none;
}
</style>

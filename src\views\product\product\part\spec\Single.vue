<template>
  <div>
    <el-form-item label="商品条码：" prop="model.sku.product_no">
      <el-input v-model="form.model.sku.productNo" class="max-w460"></el-input>
    </el-form-item>
    <el-form-item
      label="产品价格："
      width="80"
      :rules="[{ required: true, message: '请填写产品价格' }]"
      prop="model.sku.productPrice"
    >
      <el-input
        type="number"
        v-model="form.model.sku.productPrice"
        class="max-w460"
      ></el-input>
    </el-form-item>
    <el-form-item
      label="产品划线价："
      :rules="[{ required: true, message: '请填写产品划线价' }]"
      prop="model.sku.linePrice"
    >
      <el-input
        type="number"
        v-model="form.model.sku.linePrice"
        class="max-w460"
      ></el-input>
    </el-form-item>
    <el-form-item label="供应价格：" prop="model.sku.supplyPrice">
      <el-input
        type="number"
        v-model="form.model.sku.supplyPrice"
        class="max-w460"
      ></el-input>
    </el-form-item>
    <el-form-item
      label="库存数量："
      :rules="[{ required: true, message: '请填写库存数量' }]"
      prop="model.sku.stockNum"
    >
      <el-input
        type="number"
        v-model="form.model.sku.stockNum"
        class="max-w460"
      ></el-input>
    </el-form-item>
    <el-form-item
      label="商品重量(Kg)："
      :rules="[{ required: true, message: '请填写商品重量' }]"
      prop="model.sku.productWeight"
    >
      <el-input
        type="number"
        v-model="form.model.sku.productWeight"
        class="max-w460"
      ></el-input>
    </el-form-item>

    <!-- 会员卡相关字段 -->
    <template v-if="form.model.productType === 'HY'">
      <el-form-item label="消费次数：" prop="model.sku.consumeTimes">
        <el-input
          type="number"
          v-model="form.model.sku.consumeTimes"
          class="max-w460"
        ></el-input>
      </el-form-item>
      <el-form-item label="生效时间：" prop="model.sku.effectiveTime">
        <el-date-picker
          v-model="form.model.sku.effectiveTime"
          type="datetime"
          placeholder="选择生效时间"
          value-format="YYYY-MM-DD HH:mm:ss"
          class="max-w460"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="到期时间：" prop="model.sku.expireTime">
        <el-date-picker
          v-model="form.model.sku.expireTime"
          type="datetime"
          placeholder="选择到期时间"
          value-format="YYYY-MM-DD HH:mm:ss"
          class="max-w460"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="会员卡状态：" prop="model.sku.memberCardStatus">
        <el-select
          v-model="form.model.sku.memberCardStatus"
          placeholder="请选择会员卡状态"
          class="max-w460"
        >
          <el-option label="正常" value="10"></el-option>
          <el-option label="冻结" value="20"></el-option>
          <el-option label="过期" value="30"></el-option>
        </el-select>
      </el-form-item>
    </template>

    <!-- 团购相关字段 -->
    <template v-if="form.model.productType === 'TG'">
      <el-form-item label="团购状态：" prop="model.sku.groupBuyStatus">
        <el-select
          v-model="form.model.sku.groupBuyStatus"
          placeholder="请选择团购状态"
          class="max-w460"
        >
          <el-option label="待开团" value="10"></el-option>
          <el-option label="进行中" value="20"></el-option>
          <el-option label="已成团" value="30"></el-option>
          <el-option label="已结束" value="40"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发团时间：" prop="model.sku.groupStartTime">
        <el-date-picker
          v-model="form.model.sku.groupStartTime"
          type="datetime"
          placeholder="选择发团时间"
          value-format="YYYY-MM-DD HH:mm:ss"
          class="max-w460"
        ></el-date-picker>
      </el-form-item>
    </template>
  </div>
</template>

<script>
export default {
  inject: ['form'],
};
</script>

<style></style>
